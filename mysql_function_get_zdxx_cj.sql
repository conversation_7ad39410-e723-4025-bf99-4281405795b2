-- MySQL版本的get_zdxx_cj函数
-- 将MSSQL函数转换为MySQL兼容版本

DELIMITER $$

DROP FUNCTION IF EXISTS get_zdxx_cj$$

CREATE FUNCTION get_zdxx_cj(input_text TEXT) 
RETURNS TEXT
READS SQL DATA
DETERMINISTIC
BEGIN 
    DECLARE result_text TEXT DEFAULT '';
    DECLARE temp_text TEXT;
    DECLARE temp_item TEXT;
    DECLARE douhao_index INT;
    DECLARE fenhao_index INT;
    DECLARE first_douhao_index INT;
    DECLARE last_douhao_index INT;
    DECLARE temp_length INT;
    
    -- 设置初始值
    SET temp_text = input_text;
    
    -- 如果输入为空或null，返回空字符串
    IF input_text IS NULL OR input_text = '' THEN
        RETURN '';
    END IF;
    
    -- 如果没有逗号，直接返回原文本
    IF LOCATE(',', input_text) = 0 THEN
        RETURN input_text;
    END IF;
    
    -- 循环处理包含逗号的文本
    WHILE LOCATE(',', temp_text) > 0 DO
        -- 查找分号位置
        SET fenhao_index = LOCATE(';', temp_text);
        
        -- 如果找到分号，提取到分号的部分（包含分号）
        IF fenhao_index > 0 THEN
            SET temp_item = SUBSTRING(temp_text, 1, fenhao_index);
        ELSE
            -- 如果没有分号，处理剩余文本
            SET temp_item = temp_text;
        END IF;
        
        -- 如果提取的项不为空
        IF temp_item != '' THEN
            -- 查找第一个逗号位置
            SET first_douhao_index = LOCATE(',', temp_item);
            
            -- 查找最后一个逗号位置（使用REVERSE函数模拟）
            SET temp_length = CHAR_LENGTH(temp_item);
            SET last_douhao_index = temp_length - LOCATE(',', REVERSE(temp_item)) + 1;
            
            -- 从原文本中移除已处理的项
            SET temp_text = REPLACE(temp_text, temp_item, '');
            
            -- 提取逗号之间的内容并添加到结果中
            IF last_douhao_index - 3 < 0 THEN
                SET result_text = CONCAT(result_text, SUBSTRING(temp_item, first_douhao_index + 1, 0), '、');
            ELSE
                SET result_text = CONCAT(result_text, SUBSTRING(temp_item, first_douhao_index + 1, last_douhao_index - first_douhao_index - 3), '、');
            END IF;
        ELSE
            -- 如果项为空，设置结果为剩余文本并退出循环
            SET result_text = temp_text;
            LEAVE;
        END IF;
    END WHILE;
    
    -- 移除结果末尾的顿号
    IF RIGHT(result_text, 1) = '、' THEN
        SET result_text = LEFT(result_text, CHAR_LENGTH(result_text) - 1);
    END IF;
    
    RETURN result_text;
END$$

DELIMITER ;

-- 测试函数的示例用法
-- SELECT get_zdxx_cj('测试,数据;另一个,测试;') AS result;
