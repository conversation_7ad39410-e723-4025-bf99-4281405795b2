-- MySQL版本的 get_zdxx_cj 函数（修正版）
-- 功能：解析诊断信息字符串，提取诊断名称并用顿号连接
-- 输入示例：'2,冠状动脉粥样硬化性心脏病,I25.103;2,2型糖尿病,E11.900;2,急性上呼吸道感染,J06.900;'
-- 输出示例：'冠状动脉粥样硬化性心脏病、2型糖尿病、急性上呼吸道感染'

DELIMITER $$

CREATE FUNCTION `rms_get_zdxx_cj`(p_text TEXT) 
RETURNS TEXT 
READS SQL DATA
DETERMINISTIC
COMMENT '提取诊断信息并格式化'
BEGIN 
    DECLARE v_result TEXT DEFAULT '';
    DECLARE v_work_text TEXT;
    DECLARE v_segment TEXT;
    DECLARE v_fenhao_index INT;
    DECLARE v_first_douhao_index INT;
    DECLARE v_last_douhao_index INT;
    DECLARE v_extract_length INT;
    DECLARE v_extracted_text TEXT;
    DECLARE v_loop_count INT DEFAULT 0;
    DECLARE v_max_loops INT DEFAULT 100; -- 防止无限循环
    
    -- 处理空值或空字符串
    IF p_text IS NULL OR p_text = '' THEN
        RETURN '';
    END IF;
    
    -- 如果没有逗号，直接返回原文本
    IF LOCATE(',', p_text) = 0 THEN
        RETURN p_text;
    END IF;
    
    SET v_work_text = p_text;
    
    -- 循环处理每个分号分隔的段落
    WHILE LOCATE(',', v_work_text) > 0 AND v_loop_count < v_max_loops DO
        SET v_loop_count = v_loop_count + 1;
        
        -- 查找分号位置
        SET v_fenhao_index = LOCATE(';', v_work_text);
        
        IF v_fenhao_index > 0 THEN
            -- 提取到分号的部分（不包含分号）
            SET v_segment = SUBSTRING(v_work_text, 1, v_fenhao_index - 1);
            
            IF v_segment != '' THEN
                -- 查找第一个逗号位置
                SET v_first_douhao_index = LOCATE(',', v_segment);
                
                IF v_first_douhao_index > 0 THEN
                    -- 查找最后一个逗号位置（通过反向查找实现）
                    SET v_last_douhao_index = CHAR_LENGTH(v_segment) - LOCATE(',', REVERSE(v_segment)) + 1;
                    
                    -- 计算提取长度
                    SET v_extract_length = v_last_douhao_index - v_first_douhao_index - 1;
                    
                    -- 提取诊断名称部分
                    IF v_extract_length > 0 THEN
                        SET v_extracted_text = SUBSTRING(v_segment, v_first_douhao_index + 1, v_extract_length);
                        -- 只有当提取的文本不为空时才添加
                        IF TRIM(v_extracted_text) != '' THEN
                            IF v_result = '' THEN
                                SET v_result = TRIM(v_extracted_text);
                            ELSE
                                SET v_result = CONCAT(v_result, '、', TRIM(v_extracted_text));
                            END IF;
                        END IF;
                    END IF;
                END IF;
                
                -- 移除已处理的段落（包括分号）
                SET v_work_text = SUBSTRING(v_work_text, v_fenhao_index + 1);
            ELSE
                -- 如果段落为空，退出循环
                SET v_work_text = '';
            END IF;
        ELSE
            -- 没有找到分号，退出循环
            SET v_work_text = '';
        END IF;
    END WHILE;
    
    RETURN v_result;
END$$

DELIMITER ;

-- 测试用例
/*
SELECT rms_get_zdxx_cj('2,冠状动脉粥样硬化性心脏病,I25.103;2,2型糖尿病,E11.900;2,急性上呼吸道感染,J06.900;');
-- 预期输出：'冠状动脉粥样硬化性心脏病、2型糖尿病、急性上呼吸道感染'

SELECT rms_get_zdxx_cj('');
-- 预期输出：''

SELECT rms_get_zdxx_cj(NULL);
-- 预期输出：''

SELECT rms_get_zdxx_cj('简单诊断');
-- 预期输出：'简单诊断'

SELECT rms_get_zdxx_cj('1,高血压,I10.x00;');
-- 预期输出：'高血压'
*/
